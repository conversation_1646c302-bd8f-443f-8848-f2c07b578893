# 肺功能数据管理平台 - 部署指南

## 📋 目录

- [概述](#概述)
- [系统要求](#系统要求)
- [PM2部署](#pm2部署推荐)
- [腾讯云部署](#腾讯云部署)
- [环境配置](#环境配置)
- [数据库配置](#数据库配置)
- [SSL配置](#ssl配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 概述

本文档详细说明如何使用PM2进程管理器部署肺功能数据管理平台，提供稳定可靠的生产环境部署方案。

## 系统要求

### 最低配置

- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 推荐配置

- **CPU**: 4核心
- **内存**: 8GB RAM
- **磁盘**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 软件依赖

- Node.js 18+
- PM2 进程管理器
- Nginx 反向代理
- MySQL 8.0+ (数据库)

## PM2部署（推荐）

### 1. 一键部署脚本（推荐）

```bash
# 下载并执行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh | bash

# 或者手动下载执行
wget https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh
chmod +x deploy-traditional.sh
./deploy-traditional.sh
```

**一键脚本会自动完成以下操作：**
- ✅ 检测并安装Node.js 18+
- ✅ 安装PM2进程管理器
- ✅ 克隆项目代码并安装依赖
- ✅ 配置PM2进程管理
- ✅ 配置Nginx反向代理
- ✅ 设置开机自启动

### 2. 手动部署步骤

#### 2.1 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2
npm install -g pm2

# 安装Nginx
sudo apt install -y nginx
```

#### 2.2 项目部署

```bash
# 创建项目目录
sudo mkdir -p ~/www/wwwroot
cd ~/www/wwwroot

# 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 安装依赖
npm install

# 构建项目
npm run build
```

#### 2.3 配置PM2

```bash
# 配置环境变量
cp .env.example .env.production
nano .env.production

# 启动应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 2.4 配置Nginx

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/lung-function-admin
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用配置：

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 腾讯云部署

### 前提条件

- 腾讯云CVM (2核4GB Ubuntu 20.04 或更高版本)
- 安全组开放3011端口和80端口
- 腾讯云轻量数据库已配置 (MySQL)
- 确保已有GitHub账号可访问项目代码

### 腾讯云特殊配置

#### 1. 安全组配置

在腾讯云控制台配置安全组规则：

```
入站规则：
- HTTP: 80端口，来源：0.0.0.0/0
- HTTPS: 443端口，来源：0.0.0.0/0
- 自定义: 3011端口，来源：0.0.0.0/0
- SSH: 22端口，来源：你的IP
```

#### 2. 数据库连接

腾讯云轻量数据库连接配置：

```env
# 开发环境 (公网连接)
DATABASE_URL="mysql://srmyy_123:<EMAIL>:23387/srmyy_123"

# 生产环境 (内网连接，推荐)
DATABASE_URL="mysql://srmyy_123:password@*********:3306/srmyy_123"
```

#### 3. 域名和SSL配置

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 环境配置

### 必需的环境变量

```env
# 数据库连接
DATABASE_URL="mysql://username:password@host:port/database"

# 应用配置
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-nextauth-secret-key"
JWT_SECRET="your-jwt-secret-key"

# 端口配置
PORT=3011

# 环境标识
NODE_ENV=production

# 日志配置
LOG_LEVEL=info
LOG_FILE="/var/log/lung-function-admin.log"
```

### 可选的环境变量

```env
# 邮件配置
SMTP_HOST="smtp.example.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# 监控配置
SENTRY_DSN="your-sentry-dsn"
```

## 数据库配置

### MySQL配置优化

```sql
-- 创建数据库
CREATE DATABASE lung_function_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'lung_admin'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON lung_function_admin.* TO 'lung_admin'@'%';
FLUSH PRIVILEGES;
```

### 性能优化配置

在 `/etc/mysql/mysql.conf.d/mysqld.cnf` 中添加：

```ini
[mysqld]
# 连接配置
max_connections = 200
max_connect_errors = 10

# 缓存配置
innodb_buffer_pool_size = 1G
query_cache_size = 64M
query_cache_type = 1

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

## SSL配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d yourdomain.com

# 验证自动续期
sudo certbot renew --dry-run
```

### 手动SSL配置

如果使用自己的SSL证书：

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 其他配置...
}
```

## 监控和维护

### 系统监控

```bash
# 查看应用状态
pm2 status

# 查看应用日志
pm2 logs

# 查看系统资源
htop
df -h
free -h

# 查看Nginx状态
sudo systemctl status nginx

# 查看数据库状态
sudo systemctl status mysql
```

### 日志管理

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/lung-function-admin

# 内容：
/var/log/lung-function-admin/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

### 备份策略

```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p database_name > /backup/db_backup_$DATE.sql
gzip /backup/db_backup_$DATE.sql

# 文件备份
tar -czf /backup/files_backup_$DATE.tar.gz /path/to/uploads

# 清理旧备份 (保留30天)
find /backup -name "*.gz" -mtime +30 -delete
```

## 故障排除

### 常见问题

#### 1. 应用无法启动

```bash
# 检查端口占用
sudo netstat -tlnp | grep :3011

# 检查PM2日志
pm2 logs

# 检查环境变量
pm2 env 0
```

#### 2. 数据库连接失败

```bash
# 测试数据库连接
mysql -h host -P port -u username -p database

# 检查数据库服务状态
sudo systemctl status mysql

# 查看数据库错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 3. Nginx配置问题

```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 重新加载配置
sudo nginx -s reload
```

### 应用更新流程

```bash
# 1. 备份当前版本
cp -r /current/app /backup/app_$(date +%Y%m%d)

# 2. 拉取最新代码
git pull origin main

# 3. 安装依赖
npm install

# 4. 构建应用
npm run build

# 5. 运行数据库迁移
npx prisma db push

# 6. 重启应用
pm2 reload all

# 7. 验证部署
curl -f http://localhost:3011/api/health
```

---

## 📞 支持

如果在部署过程中遇到问题：

1. 📖 查看[故障排除](#故障排除)部分
2. 🐛 提交[Issue](../../issues)
3. 💬 参与[讨论](../../discussions)
4. 📧 联系技术支持

---

**部署成功后，请访问您的域名验证应用是否正常运行！**
